import axios from 'axios';
import testConfig from './test-config.js';

// 创建 axios 客户端
const client = axios.create({
  baseURL: testConfig.baseUrl,
  headers: {
    'Authorization': `Bearer ${testConfig.token}`,
    'X-App': testConfig.app,
    'Content-Type': 'application/json'
  }
});

async function testCorrectGrid() {
  console.log('🔍 测试使用正确的Grid UID插入区块');
  
  const pageUid = '2mk30f1pasa'; // "123" 页面的 UID
  const gridUid = 'v4pv8gtz2s3'; // 从API获取的正确Grid UID
  
  try {
    // 1. 首先获取完整的页面Schema
    console.log('\n📋 Step 1: Get complete page schema');
    const schemaResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}?includeAsyncNode=true`);
    console.log('✅ Complete schema:');
    console.log(JSON.stringify(schemaResponse.data.data, null, 2));

    // 2. 尝试插入到正确的Grid UID
    console.log('\n📋 Step 2: Insert block to correct Grid UID');
    
    // 创建一个最简单的区块Schema，不设置name属性
    const blockSchema = {
      type: 'void',
      'x-component': 'CardItem',
      'x-component-props': {
        title: '测试区块 - 正确Grid UID'
      },
      'x-uid': `test-block-${Date.now()}`,
      properties: {
        content: {
          type: 'void',
          'x-component': 'Markdown.Void',
          'x-component-props': {
            content: '# 成功！\n\n这个区块使用了正确的Grid UID。'
          },
          'x-uid': `content-${Date.now()}`
        }
      }
    };

    try {
      const insertResponse = await client.post('/uiSchemas:insertAdjacent', {
        parentUid: gridUid, // 使用正确的Grid UID
        schema: blockSchema,
        position: 'beforeEnd'
      });
      console.log('✅ Insert successful:');
      console.log(JSON.stringify(insertResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Insert failed:');
      console.log('Status:', error.response?.status);
      console.log('Data:', JSON.stringify(error.response?.data, null, 2));
    }

    // 3. 尝试更简单的Schema，完全不设置name
    console.log('\n📋 Step 3: Try even simpler schema without name');
    
    const simpleSchema = {
      type: 'void',
      'x-component': 'Markdown.Void',
      'x-component-props': {
        content: '# 简单测试\n\n最简单的区块。'
      },
      'x-uid': `simple-${Date.now()}`
    };

    try {
      const simpleResponse = await client.post('/uiSchemas:insertAdjacent', {
        parentUid: gridUid,
        schema: simpleSchema,
        position: 'beforeEnd'
      });
      console.log('✅ Simple insert successful:');
      console.log(JSON.stringify(simpleResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Simple insert failed:');
      console.log('Status:', error.response?.status);
      console.log('Data:', JSON.stringify(error.response?.data, null, 2));
    }

    // 4. 检查Grid的当前内容
    console.log('\n📋 Step 4: Check Grid properties');
    try {
      const gridResponse = await client.get(`/uiSchemas:getProperties/${gridUid}`);
      console.log('✅ Grid properties:');
      console.log(JSON.stringify(gridResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Get Grid properties failed:', error.response?.data || error.message);
    }

    // 5. 尝试使用NocoBase的UID生成函数
    console.log('\n📋 Step 5: Try with NocoBase UID generation');
    
    // 简单的UID生成（模拟NocoBase的方式）
    function generateUID() {
      return Math.random().toString(36).substring(2, 15);
    }

    const uidSchema = {
      type: 'void',
      'x-component': 'CardItem',
      'x-component-props': {
        title: '使用生成UID的测试区块'
      },
      'x-uid': generateUID(),
      properties: {
        [generateUID()]: {
          type: 'void',
          'x-component': 'Markdown.Void',
          'x-component-props': {
            content: '# UID生成测试\n\n使用生成的UID。'
          },
          'x-uid': generateUID()
        }
      }
    };

    try {
      const uidResponse = await client.post('/uiSchemas:insertAdjacent', {
        parentUid: gridUid,
        schema: uidSchema,
        position: 'beforeEnd'
      });
      console.log('✅ UID generation insert successful:');
      console.log(JSON.stringify(uidResponse.data, null, 2));
    } catch (error) {
      console.log('❌ UID generation insert failed:');
      console.log('Status:', error.response?.status);
      console.log('Data:', JSON.stringify(error.response?.data, null, 2));
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testCorrectGrid().catch(console.error);
