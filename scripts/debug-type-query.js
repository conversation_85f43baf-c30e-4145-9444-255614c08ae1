import axios from 'axios';
import testConfig from './test-config.js';

// 创建 axios 客户端
const client = axios.create({
  baseURL: testConfig.baseUrl,
  headers: {
    'Authorization': `Bearer ${testConfig.token}`,
    'X-App': testConfig.app,
    'Content-Type': 'application/json'
  }
});

async function debugTypeQuery() {
  console.log('🔍 调试 typeQuery 问题');
  
  const pageUid = '2mk30f1pasa'; // "123" 页面的 UID
  const gridUid = 'v4pv8gtz2s3'; // Grid UID
  
  try {
    // 1. 检查 Grid 是否存在于 uiSchemaTreePath 表中
    console.log('\n📋 Step 1: Check if Grid exists in tree path');
    
    // 直接查询数据库（通过API模拟）
    // 我们需要检查 NocoBase 的数据库结构
    
    // 2. 尝试获取 Grid 的详细信息
    console.log('\n📋 Step 2: Get Grid details');
    try {
      const gridResponse = await client.get(`/uiSchemas:getJsonSchema/${gridUid}?includeAsyncNode=true`);
      console.log('✅ Grid schema:');
      console.log(JSON.stringify(gridResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Get Grid schema failed:', error.response?.data || error.message);
    }

    // 3. 尝试获取 Grid 的 properties
    console.log('\n📋 Step 3: Get Grid properties');
    try {
      const gridPropsResponse = await client.get(`/uiSchemas:getProperties/${gridUid}`);
      console.log('✅ Grid properties:');
      console.log(JSON.stringify(gridPropsResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Get Grid properties failed:', error.response?.data || error.message);
    }

    // 4. 尝试使用不同的插入方式
    console.log('\n📋 Step 4: Try different insertion methods');
    
    // 4.1 尝试使用 insert 而不是 insertAdjacent
    const insertSchema = {
      type: 'void',
      'x-component': 'Markdown.Void',
      'x-component-props': {
        content: '# 直接插入测试\n\n使用insert方法。'
      },
      'x-uid': `direct-insert-${Date.now()}`,
      name: `direct-insert-${Date.now()}`
    };

    try {
      const insertResponse = await client.post('/uiSchemas:insert', {
        values: insertSchema
      });
      console.log('✅ Direct insert successful:');
      console.log(JSON.stringify(insertResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Direct insert failed:');
      console.log('Status:', error.response?.status);
      console.log('Data:', JSON.stringify(error.response?.data, null, 2));
    }

    // 4.2 尝试使用 patch 方法添加到 Grid
    console.log('\n📋 Step 4.2: Try patch method');
    
    const patchSchema = {
      'x-uid': gridUid,
      properties: {
        [`patch-test-${Date.now()}`]: {
          type: 'void',
          'x-component': 'CardItem',
          'x-component-props': {
            title: 'Patch 测试区块'
          },
          'x-uid': `patch-block-${Date.now()}`,
          properties: {
            content: {
              type: 'void',
              'x-component': 'Markdown.Void',
              'x-component-props': {
                content: '# Patch 测试\n\n使用patch方法添加。'
              },
              'x-uid': `patch-content-${Date.now()}`
            }
          }
        }
      }
    };

    try {
      const patchResponse = await client.post('/uiSchemas:patch', patchSchema);
      console.log('✅ Patch successful:');
      console.log(JSON.stringify(patchResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Patch failed:');
      console.log('Status:', error.response?.status);
      console.log('Data:', JSON.stringify(error.response?.data, null, 2));
    }

    // 5. 检查页面是否有变化
    console.log('\n📋 Step 5: Check page changes');
    try {
      const finalPageResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}?includeAsyncNode=true`);
      console.log('✅ Final page schema:');
      console.log(JSON.stringify(finalPageResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Get final page schema failed:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  }
}

debugTypeQuery().catch(console.error);
