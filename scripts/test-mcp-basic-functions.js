import { spawn } from 'child_process';
import testConfig from './test-config.js';

let requestId = 1;

// MCP 客户端
class MCPClient {
  constructor(server) {
    this.server = server;
    this.pendingRequests = new Map();
    this.setupResponseHandler();
  }

  setupResponseHandler() {
    let buffer = '';
    this.server.stdout.on('data', (data) => {
      buffer += data.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      lines.forEach(line => {
        if (line.trim()) {
          try {
            const response = JSON.parse(line);
            this.handleResponse(response);
          } catch (e) {
            // 忽略非 JSON 输出
          }
        }
      });
    });

    this.server.stderr.on('data', (data) => {
      console.log('🔍 Server log:', data.toString());
    });
  }

  handleResponse(response) {
    if (response.id && this.pendingRequests.has(response.id)) {
      const { resolve, reject } = this.pendingRequests.get(response.id);
      this.pendingRequests.delete(response.id);
      
      if (response.error) {
        reject(new Error(response.error.message || 'Unknown error'));
      } else {
        resolve(response.result);
      }
    }
  }

  async sendRequest(method, params = {}, timeout = 15000) {
    const id = requestId++;
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      ...(Object.keys(params).length > 0 && { params })
    };

    console.log(`\n📤 REQUEST: ${method}`);
    if (Object.keys(params).length > 0) {
      console.log('📋 Params:', JSON.stringify(params, null, 2));
    }
    
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject, timestamp: Date.now() });
      
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request ${id} timed out after ${timeout}ms`));
        }
      }, timeout);
      
      this.server.stdin.write(JSON.stringify(request) + '\n');
    });
  }
}

function startMCPServer() {
  console.log('🚀 Starting MCP server...');
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', testConfig.baseUrl,
    '--token', testConfig.token,
    '--app', testConfig.app
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  return server;
}

async function testMCPBasicFunctions() {
  const server = startMCPServer();
  const mcpClient = new MCPClient(server);
  
  try {
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n=== 🧪 MCP服务器基础功能测试 ===\n');

    // 1. 初始化
    console.log('📋 Test 1: Initialize MCP Server');
    const initResult = await mcpClient.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'test-client', version: '1.0.0' }
    });
    console.log('✅ Initialize successful');

    // 2. 列出可用工具
    console.log('\n📋 Test 2: List Available Tools');
    const toolsResult = await mcpClient.sendRequest('tools/list');
    console.log('✅ Available tools count:', toolsResult.tools.length);
    console.log('📋 Tool names:', toolsResult.tools.map(t => t.name).join(', '));

    // 3. 测试路由列表功能
    console.log('\n📋 Test 3: List Routes');
    const routesResult = await mcpClient.sendRequest('tools/call', {
      name: 'list_routes',
      arguments: {}
    });
    console.log('✅ Routes response received');
    if (routesResult.content && routesResult.content[0]) {
      const routesText = routesResult.content[0].text;
      const routeCount = (routesText.match(/Found \d+ routes/)?.[0] || 'Unknown count');
      console.log('📊', routeCount);
    }

    // 4. 测试集合列表功能
    console.log('\n📋 Test 4: List Collections');
    const collectionsResult = await mcpClient.sendRequest('tools/call', {
      name: 'list_collections',
      arguments: {}
    });
    console.log('✅ Collections response received');
    if (collectionsResult.content && collectionsResult.content[0]) {
      const collectionsText = collectionsResult.content[0].text;
      const collectionCount = (collectionsText.match(/Found \d+ collections/)?.[0] || 'Unknown count');
      console.log('📊', collectionCount);
    }

    // 5. 测试页面Schema读取功能
    console.log('\n📋 Test 5: Get Page Schema');
    const pageUid = '2mk30f1pasa'; // "123" 页面
    const schemaResult = await mcpClient.sendRequest('tools/call', {
      name: 'get_page_schema',
      arguments: { schemaUid: pageUid }
    });
    console.log('✅ Page schema response received');
    if (schemaResult.content && schemaResult.content[0]) {
      const schemaText = schemaResult.content[0].text;
      const hasGrid = schemaText.includes('Grid');
      console.log('📊 Contains Grid component:', hasGrid);
    }

    // 6. 测试Students集合数据读取
    console.log('\n📋 Test 6: List Students Records');
    const studentsResult = await mcpClient.sendRequest('tools/call', {
      name: 'list_records',
      arguments: { collection: 'students' }
    });
    console.log('✅ Students records response received');
    if (studentsResult.content && studentsResult.content[0]) {
      const studentsText = studentsResult.content[0].text;
      const recordCount = (studentsText.match(/Found \d+ records/)?.[0] || 'Unknown count');
      console.log('📊', recordCount);
    }

    // 7. 测试区块类型列表
    console.log('\n📋 Test 7: List Block Types');
    const blockTypesResult = await mcpClient.sendRequest('tools/call', {
      name: 'list_block_types',
      arguments: {}
    });
    console.log('✅ Block types response received');
    if (blockTypesResult.content && blockTypesResult.content[0]) {
      const blockTypesText = blockTypesResult.content[0].text;
      const hasTable = blockTypesText.includes('table');
      const hasMarkdown = blockTypesText.includes('markdown');
      console.log('📊 Has table block:', hasTable);
      console.log('📊 Has markdown block:', hasMarkdown);
    }

    // 8. 测试页面区块列表
    console.log('\n📋 Test 8: List Page Blocks');
    const pageBlocksResult = await mcpClient.sendRequest('tools/call', {
      name: 'list_page_blocks',
      arguments: { schemaUid: pageUid }
    });
    console.log('✅ Page blocks response received');
    if (pageBlocksResult.content && pageBlocksResult.content[0]) {
      const blocksText = pageBlocksResult.content[0].text;
      const blockCount = (blocksText.match(/Found \d+ blocks/)?.[0] || 'Unknown count');
      console.log('📊', blockCount);
    }

    console.log('\n🎉 所有基础功能测试完成！');
    console.log('\n📊 测试总结：');
    console.log('✅ MCP协议初始化 - 正常');
    console.log('✅ 工具列表获取 - 正常');
    console.log('✅ 路由管理 - 正常');
    console.log('✅ 集合管理 - 正常');
    console.log('✅ Schema读取 - 正常');
    console.log('✅ 数据读取 - 正常');
    console.log('✅ 区块类型管理 - 正常');
    console.log('✅ 页面区块列表 - 正常');

  } catch (error) {
    console.error('\n❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    console.log('\n🔌 Shutting down server...');
    server.kill();
  }
}

// 启动测试
testMCPBasicFunctions().catch(console.error);
