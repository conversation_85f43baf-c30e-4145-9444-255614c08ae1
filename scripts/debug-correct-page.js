import { spawn } from 'child_process';
import testConfig from './test-config.js';

let requestId = 1;

// MCP 客户端
class MCPClient {
  constructor(server) {
    this.server = server;
    this.pendingRequests = new Map();
    this.setupResponseHandler();
  }

  setupResponseHandler() {
    let buffer = '';
    this.server.stdout.on('data', (data) => {
      buffer += data.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      lines.forEach(line => {
        if (line.trim()) {
          try {
            const response = JSON.parse(line);
            this.handleResponse(response);
          } catch (e) {
            // 忽略非 JSON 输出
          }
        }
      });
    });

    this.server.stderr.on('data', (data) => {
      console.log('🔍 Server log:', data.toString());
    });
  }

  handleResponse(response) {
    if (response.id && this.pendingRequests.has(response.id)) {
      const { resolve, reject } = this.pendingRequests.get(response.id);
      this.pendingRequests.delete(response.id);
      
      if (response.error) {
        reject(new Error(response.error.message || 'Unknown error'));
      } else {
        resolve(response.result);
      }
    }
  }

  async sendRequest(method, params = {}, timeout = 15000) {
    const id = requestId++;
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      ...(Object.keys(params).length > 0 && { params })
    };

    console.log(`\n📤 REQUEST: ${method}`);
    console.log('📋 Params:', JSON.stringify(params, null, 2));
    
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject, timestamp: Date.now() });
      
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request ${id} timed out after ${timeout}ms`));
        }
      }, timeout);
      
      this.server.stdin.write(JSON.stringify(request) + '\n');
    });
  }
}

function startMCPServer() {
  console.log('🚀 Starting MCP server...');
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', testConfig.baseUrl,
    '--token', testConfig.token,
    '--app', testConfig.app
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  return server;
}

async function debugCorrectPage() {
  const server = startMCPServer();
  const mcpClient = new MCPClient(server);
  
  try {
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n=== 🔍 使用正确页面UID调试Schema API插入问题 ===\n');

    // 1. 初始化
    console.log('📋 Step 1: Initialize');
    await mcpClient.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'debug-client', version: '1.0.0' }
    });

    // 2. 使用正确的页面UID（"123"页面）
    const correctPageUid = '2mk30f1pasa';
    console.log(`\n📋 Step 2: Get correct page schema`);
    console.log(`🎯 Using correct page UID: ${correctPageUid}`);
    
    const schemaResult = await mcpClient.sendRequest('tools/call', {
      name: 'get_page_schema',
      arguments: { schemaUid: correctPageUid }
    });

    console.log('\n📥 SCHEMA RESPONSE:');
    if (schemaResult && schemaResult.content && schemaResult.content[0]) {
      const responseText = schemaResult.content[0].text;
      console.log('📄 Response text:');
      console.log(responseText);
      
      // 尝试解析 JSON
      const match = responseText.match(/Page schema retrieved successfully:\s*(\{[\s\S]*\})/);
      if (match) {
        try {
          const schema = JSON.parse(match[1]);
          console.log('\n🔍 Schema analysis:');
          console.log('- Root UID:', schema['x-uid']);
          console.log('- Root component:', schema['x-component']);
          console.log('- Has schema property:', !!schema.schema);
          
          if (schema.schema && schema.schema.properties) {
            console.log('- Schema properties keys:', Object.keys(schema.schema.properties));
            
            // 查找Grid组件
            const grid = schema.schema.properties.grid;
            if (grid) {
              console.log('\n📊 Grid component found:');
              console.log('- Grid component:', grid['x-component']);
              console.log('- Grid initializer:', grid['x-initializer']);
              console.log('- Grid has properties:', !!grid.properties);
              
              if (grid.properties) {
                console.log('- Grid properties count:', Object.keys(grid.properties).length);
                console.log('- Grid properties keys:', Object.keys(grid.properties));
              }
            } else {
              console.log('❌ No Grid component found in schema');
            }
          }
        } catch (e) {
          console.log('❌ Failed to parse schema JSON:', e.message);
        }
      }
    }

    // 3. 尝试添加最简单的Markdown区块
    console.log('\n📋 Step 3: Try adding simplest Markdown block');
    
    try {
      const addResult = await mcpClient.sendRequest('tools/call', {
        name: 'add_markdown_block',
        arguments: {
          parentUid: correctPageUid,
          title: '🔍 调试测试区块',
          content: '# 测试区块\n\n这是一个测试区块。',
          position: 'beforeEnd'
        }
      });
      
      console.log('\n📥 ADD BLOCK RESPONSE:');
      console.log(JSON.stringify(addResult, null, 2));
      
    } catch (error) {
      console.log('\n❌ ADD BLOCK ERROR:');
      console.log('Error message:', error.message);
    }

    // 4. 检查区块是否被添加
    console.log('\n📋 Step 4: Check if block was added');
    
    try {
      const listResult = await mcpClient.sendRequest('tools/call', {
        name: 'list_page_blocks',
        arguments: { schemaUid: correctPageUid }
      });
      
      console.log('\n📥 LIST BLOCKS RESPONSE:');
      console.log(JSON.stringify(listResult, null, 2));
      
    } catch (error) {
      console.log('\n❌ LIST BLOCKS ERROR:');
      console.log('Error message:', error.message);
    }

    console.log('\n📋 调试完成！');

  } catch (error) {
    console.error('\n❌ Debug failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    console.log('\n🔌 Shutting down server...');
    server.kill();
  }
}

// 启动调试
debugCorrectPage().catch(console.error);
