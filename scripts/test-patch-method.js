import axios from 'axios';
import testConfig from './test-config.js';

// 创建 axios 客户端
const client = axios.create({
  baseURL: testConfig.baseUrl,
  headers: {
    'Authorization': `Bearer ${testConfig.token}`,
    'X-App': testConfig.app,
    'Content-Type': 'application/json'
  }
});

function generateUID() {
  return Math.random().toString(36).substring(2, 15);
}

async function testPatchMethod() {
  console.log('🔍 测试 patch 方法添加区块到 Grid');
  
  const pageUid = '2mk30f1pasa';
  const gridUid = 'v4pv8gtz2s3';
  
  try {
    // 1. 首先创建一个区块
    console.log('\n📋 Step 1: Create a block using insert');
    
    const blockSchema = {
      type: 'void',
      'x-component': 'CardItem',
      'x-component-props': {
        title: 'Patch测试区块'
      },
      'x-uid': generateUID(),
      name: generateUID(),
      properties: {
        content: {
          type: 'void',
          'x-component': 'Markdown.Void',
          'x-component-props': {
            content: '# Patch测试\n\n这个区块通过patch方法添加到Grid。'
          },
          'x-uid': generateUID(),
          name: generateUID()
        }
      }
    };

    const insertResponse = await client.post('/uiSchemas:insert', {
      values: blockSchema
    });

    console.log('✅ Block created successfully');
    console.log('📊 Insert response structure:');
    console.log('- Wrapper UID:', insertResponse.data.data['x-uid']);
    console.log('- Block UID:', insertResponse.data.data.values['x-uid']);

    const createdBlock = insertResponse.data.data.values;

    // 2. 获取Grid的当前状态
    console.log('\n📋 Step 2: Get Grid current state');
    
    const gridResponse = await client.get(`/uiSchemas:getJsonSchema/${gridUid}?includeAsyncNode=true`);
    console.log('✅ Grid schema retrieved');
    console.log('📊 Grid current properties:', Object.keys(gridResponse.data.data.properties || {}));

    // 3. 尝试使用patch方法添加区块到Grid
    console.log('\n📋 Step 3: Patch block to Grid');
    
    const blockKey = `block-${Date.now()}`;
    const patchData = {
      'x-uid': gridUid,
      properties: {
        [blockKey]: createdBlock
      }
    };

    console.log('📋 Patch data:');
    console.log(JSON.stringify(patchData, null, 2));

    try {
      const patchResponse = await client.post('/uiSchemas:patch', patchData);
      console.log('✅ Patch successful!');
      console.log('📊 Patch response:', JSON.stringify(patchResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Patch failed:');
      console.log('Status:', error.response?.status);
      console.log('Data:', JSON.stringify(error.response?.data, null, 2));
    }

    // 4. 检查Grid的最终状态
    console.log('\n📋 Step 4: Check Grid final state');
    
    const finalGridResponse = await client.get(`/uiSchemas:getJsonSchema/${gridUid}?includeAsyncNode=true`);
    console.log('✅ Final Grid schema retrieved');
    console.log('📊 Final Grid properties:', Object.keys(finalGridResponse.data.data.properties || {}));

    // 5. 检查页面的最终状态
    console.log('\n📋 Step 5: Check page final state');
    
    const finalPageResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}?includeAsyncNode=true`);
    console.log('✅ Final page schema retrieved');
    
    const pageSchema = finalPageResponse.data.data;
    if (pageSchema.properties && pageSchema.properties.ljascd3pvvg) {
      const grid = pageSchema.properties.ljascd3pvvg;
      console.log('📊 Page Grid properties:', Object.keys(grid.properties || {}));
      
      if (grid.properties && Object.keys(grid.properties).length > 0) {
        console.log('🎉 SUCCESS! Block was added to Grid!');
      } else {
        console.log('😞 Block was not added to Grid');
      }
    }

    // 6. 尝试另一种patch方法 - 直接修改页面Schema
    console.log('\n📋 Step 6: Try patching page schema directly');
    
    const pageBlockKey = `page-block-${Date.now()}`;
    const pagePatchData = {
      'x-uid': pageUid,
      properties: {
        ljascd3pvvg: {
          ...pageSchema.properties.ljascd3pvvg,
          properties: {
            ...(pageSchema.properties.ljascd3pvvg.properties || {}),
            [pageBlockKey]: {
              type: 'void',
              'x-component': 'CardItem',
              'x-component-props': {
                title: '直接页面Patch测试'
              },
              'x-uid': generateUID(),
              properties: {
                content: {
                  type: 'void',
                  'x-component': 'Markdown.Void',
                  'x-component-props': {
                    content: '# 直接页面Patch\n\n这个区块通过直接patch页面Schema添加。'
                  },
                  'x-uid': generateUID()
                }
              }
            }
          }
        }
      }
    };

    try {
      const pagePatchResponse = await client.post('/uiSchemas:patch', pagePatchData);
      console.log('✅ Page patch successful!');
      console.log('📊 Page patch response:', JSON.stringify(pagePatchResponse.data, null, 2));
    } catch (error) {
      console.log('❌ Page patch failed:');
      console.log('Status:', error.response?.status);
      console.log('Data:', JSON.stringify(error.response?.data, null, 2));
    }

    // 7. 最终检查
    console.log('\n📋 Step 7: Final verification');
    
    const veryFinalPageResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}?includeAsyncNode=true`);
    const veryFinalPageSchema = veryFinalPageResponse.data.data;
    
    if (veryFinalPageSchema.properties && veryFinalPageSchema.properties.ljascd3pvvg) {
      const finalGrid = veryFinalPageSchema.properties.ljascd3pvvg;
      const finalBlockCount = Object.keys(finalGrid.properties || {}).length;
      console.log('📊 Final block count in Grid:', finalBlockCount);
      
      if (finalBlockCount > 0) {
        console.log('🎊 AMAZING! We successfully added blocks to the Grid!');
        console.log('🔗 Check the page at: https://n.astra.xin/apps/mcp_playground/admin/123');
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testPatchMethod().catch(console.error);
