import axios from 'axios';
import testConfig from './test-config.js';

// 创建 axios 客户端
const client = axios.create({
  baseURL: testConfig.baseUrl,
  headers: {
    'Authorization': `Bearer ${testConfig.token}`,
    'X-App': testConfig.app,
    'Content-Type': 'application/json'
  }
});

async function testDirectAPI() {
  console.log('🔍 直接测试 NocoBase API');
  console.log('Base URL:', testConfig.baseUrl);
  console.log('App:', testConfig.app);
  
  const pageUid = '2mk30f1pasa'; // "123" 页面的 UID
  
  try {
    // 1. 测试不同的 Schema API 调用方式
    console.log('\n📋 Test 1: getJsonSchema API');
    try {
      const response1 = await client.get(`/uiSchemas:getJsonSchema/${pageUid}`);
      console.log('✅ getJsonSchema response:');
      console.log(JSON.stringify(response1.data, null, 2));
    } catch (error) {
      console.log('❌ getJsonSchema failed:', error.response?.data || error.message);
    }

    // 2. 测试 getProperties API
    console.log('\n📋 Test 2: getProperties API');
    try {
      const response2 = await client.get(`/uiSchemas:getProperties/${pageUid}`);
      console.log('✅ getProperties response:');
      console.log(JSON.stringify(response2.data, null, 2));
    } catch (error) {
      console.log('❌ getProperties failed:', error.response?.data || error.message);
    }

    // 3. 测试直接获取 schema
    console.log('\n📋 Test 3: Direct schema get');
    try {
      const response3 = await client.get(`/uiSchemas/${pageUid}`);
      console.log('✅ Direct schema response:');
      console.log(JSON.stringify(response3.data, null, 2));
    } catch (error) {
      console.log('❌ Direct schema failed:', error.response?.data || error.message);
    }

    // 4. 测试 getJsonSchema 带参数
    console.log('\n📋 Test 4: getJsonSchema with includeAsyncNode');
    try {
      const response4 = await client.get(`/uiSchemas:getJsonSchema/${pageUid}?includeAsyncNode=true`);
      console.log('✅ getJsonSchema with includeAsyncNode response:');
      console.log(JSON.stringify(response4.data, null, 2));
    } catch (error) {
      console.log('❌ getJsonSchema with includeAsyncNode failed:', error.response?.data || error.message);
    }

    // 5. 测试创建一个最简单的 Schema
    console.log('\n📋 Test 5: Create simplest schema');
    const simpleSchema = {
      type: 'void',
      'x-component': 'Markdown.Void',
      'x-component-props': {
        content: '# 测试区块\n\n这是一个直接API测试区块。'
      },
      'x-uid': `test-${Date.now()}`,
      name: `test-${Date.now()}`
    };

    try {
      const response5 = await client.post('/uiSchemas:insertAdjacent', {
        parentUid: pageUid,
        schema: simpleSchema,
        position: 'beforeEnd'
      });
      console.log('✅ insertAdjacent response:');
      console.log(JSON.stringify(response5.data, null, 2));
    } catch (error) {
      console.log('❌ insertAdjacent failed:');
      console.log('Status:', error.response?.status);
      console.log('Data:', JSON.stringify(error.response?.data, null, 2));
      console.log('Message:', error.message);
    }

    // 6. 测试插入到 Grid 中
    console.log('\n📋 Test 6: Insert into Grid');
    const gridSchema = {
      type: 'void',
      'x-component': 'CardItem',
      'x-component-props': {
        title: '直接API测试区块'
      },
      'x-uid': `grid-test-${Date.now()}`,
      name: `grid-test-${Date.now()}`,
      properties: {
        content: {
          type: 'void',
          'x-component': 'Markdown.Void',
          'x-component-props': {
            content: '# 直接API测试\n\n这个区块是通过直接API调用创建的。'
          },
          'x-uid': `content-${Date.now()}`,
          name: `content-${Date.now()}`
        }
      }
    };

    try {
      const response6 = await client.post('/uiSchemas:insertAdjacent', {
        parentUid: `${pageUid}.grid`,
        schema: gridSchema,
        position: 'beforeEnd'
      });
      console.log('✅ Insert into Grid response:');
      console.log(JSON.stringify(response6.data, null, 2));
    } catch (error) {
      console.log('❌ Insert into Grid failed:');
      console.log('Status:', error.response?.status);
      console.log('Data:', JSON.stringify(error.response?.data, null, 2));
      console.log('Message:', error.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testDirectAPI().catch(console.error);
