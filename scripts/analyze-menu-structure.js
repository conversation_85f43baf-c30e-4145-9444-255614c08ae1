import { spawn } from 'child_process';
import testConfig from './test-config.js';

let requestId = 1;

// MCP 客户端
class MCPClient {
  constructor(server) {
    this.server = server;
    this.pendingRequests = new Map();
    this.setupResponseHandler();
  }

  setupResponseHandler() {
    let buffer = '';
    this.server.stdout.on('data', (data) => {
      buffer += data.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      lines.forEach(line => {
        if (line.trim()) {
          try {
            const response = JSON.parse(line);
            this.handleResponse(response);
          } catch (e) {
            // 忽略非 JSON 输出
          }
        }
      });
    });

    this.server.stderr.on('data', (data) => {
      console.log('🔍 Server log:', data.toString());
    });
  }

  handleResponse(response) {
    if (response.id && this.pendingRequests.has(response.id)) {
      const { resolve, reject } = this.pendingRequests.get(response.id);
      this.pendingRequests.delete(response.id);
      
      if (response.error) {
        reject(new Error(response.error.message || 'Unknown error'));
      } else {
        resolve(response.result);
      }
    }
  }

  async sendRequest(method, params = {}, timeout = 15000) {
    const id = requestId++;
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      ...(Object.keys(params).length > 0 && { params })
    };

    console.log(`\n📤 REQUEST: ${method}`);
    if (Object.keys(params).length > 0) {
      console.log('📋 Params:', JSON.stringify(params, null, 2));
    }
    
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject, timestamp: Date.now() });
      
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request ${id} timed out after ${timeout}ms`));
        }
      }, timeout);
      
      this.server.stdin.write(JSON.stringify(request) + '\n');
    });
  }
}

function startMCPServer() {
  console.log('🚀 Starting MCP server...');
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', testConfig.baseUrl,
    '--token', testConfig.token,
    '--app', testConfig.app
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  return server;
}

function analyzeRouteStructure(routes) {
  console.log('\n🔍 分析路由结构：');
  
  const rootRoutes = routes.filter(route => !route.parentId);
  const childRoutes = routes.filter(route => route.parentId);
  
  console.log(`📊 总路由数: ${routes.length}`);
  console.log(`📊 根路由数: ${rootRoutes.length}`);
  console.log(`📊 子路由数: ${childRoutes.length}`);
  
  console.log('\n📁 根路由列表：');
  rootRoutes.forEach((route, index) => {
    console.log(`${index + 1}. ${route.title || 'Untitled'}`);
    console.log(`   - ID: ${route.id}`);
    console.log(`   - Type: ${route.type}`);
    console.log(`   - Schema UID: ${route.schemaUid}`);
    console.log(`   - Menu Schema UID: ${route.menuSchemaUid}`);
    console.log(`   - Sort: ${route.sort}`);
    console.log(`   - Hidden: ${route.hidden || false}`);
    
    // 查找子路由
    const children = childRoutes.filter(child => child.parentId === route.id);
    if (children.length > 0) {
      console.log(`   - 子路由数: ${children.length}`);
      children.forEach((child, childIndex) => {
        console.log(`     ${childIndex + 1}. ${child.title || 'Untitled'}`);
        console.log(`        - ID: ${child.id}`);
        console.log(`        - Type: ${child.type}`);
        console.log(`        - Schema UID: ${child.schemaUid}`);
        console.log(`        - Tab Schema Name: ${child.tabSchemaName}`);
      });
    }
    console.log('');
  });
  
  return { rootRoutes, childRoutes };
}

async function analyzeMenuStructure() {
  const server = startMCPServer();
  const mcpClient = new MCPClient(server);
  
  try {
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n=== 🗂️ NocoBase 菜单结构分析 ===\n');

    // 1. 初始化
    console.log('📋 Step 1: Initialize MCP Server');
    await mcpClient.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'menu-analyzer', version: '1.0.0' }
    });

    // 2. 获取完整的路由树结构
    console.log('\n📋 Step 2: Get Complete Route Tree Structure');
    const routesResult = await mcpClient.sendRequest('tools/call', {
      name: 'list_routes',
      arguments: { tree: true }
    });

    if (routesResult.content && routesResult.content[0]) {
      const routesText = routesResult.content[0].text;
      console.log('✅ Routes response received');
      
      // 尝试解析路由数据
      try {
        // 从响应文本中提取JSON数据
        const jsonMatch = routesText.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          const routes = JSON.parse(jsonMatch[0]);
          console.log(`📊 解析到 ${routes.length} 个路由`);
          
          // 分析路由结构
          const { rootRoutes, childRoutes } = analyzeRouteStructure(routes);
          
          // 3. 分析每个页面的Schema结构
          console.log('\n📋 Step 3: Analyze Page Schema for Each Route');
          
          for (const route of rootRoutes) {
            if (route.type === 'page' && route.schemaUid) {
              console.log(`\n🔍 分析页面: ${route.title} (${route.schemaUid})`);
              
              try {
                const schemaResult = await mcpClient.sendRequest('tools/call', {
                  name: 'get_page_schema',
                  arguments: { schemaUid: route.schemaUid }
                });
                
                if (schemaResult.content && schemaResult.content[0]) {
                  const schemaText = schemaResult.content[0].text;
                  
                  // 分析Schema内容
                  const hasGrid = schemaText.includes('Grid');
                  const hasProperties = schemaText.includes('properties');
                  const hasBlocks = schemaText.includes('CardItem');
                  
                  console.log(`   ✅ Schema获取成功`);
                  console.log(`   📊 包含Grid组件: ${hasGrid}`);
                  console.log(`   📊 包含Properties: ${hasProperties}`);
                  console.log(`   📊 包含区块: ${hasBlocks}`);
                  
                  // 如果是"123"页面，进行详细分析
                  if (route.title === '123') {
                    console.log(`\n🎯 详细分析目标页面 "${route.title}"`);
                    
                    // 获取页面区块列表
                    const blocksResult = await mcpClient.sendRequest('tools/call', {
                      name: 'list_page_blocks',
                      arguments: { schemaUid: route.schemaUid }
                    });
                    
                    if (blocksResult.content && blocksResult.content[0]) {
                      const blocksText = blocksResult.content[0].text;
                      const blockCount = (blocksText.match(/Found (\d+) blocks/)?.[1] || '0');
                      console.log(`   📊 当前区块数量: ${blockCount}`);
                      
                      if (parseInt(blockCount) === 0) {
                        console.log(`   💡 这个页面是空的，适合添加Students表格！`);
                      }
                    }
                  }
                }
              } catch (error) {
                console.log(`   ❌ Schema分析失败: ${error.message}`);
              }
            }
          }
          
          // 4. 分析集合信息
          console.log('\n📋 Step 4: Analyze Collections');
          const collectionsResult = await mcpClient.sendRequest('tools/call', {
            name: 'list_collections',
            arguments: {}
          });
          
          if (collectionsResult.content && collectionsResult.content[0]) {
            const collectionsText = collectionsResult.content[0].text;
            console.log('✅ Collections response received');
            
            // 检查Students集合
            if (collectionsText.includes('students')) {
              console.log('🎓 找到Students集合！');
              
              // 获取Students数据
              const studentsResult = await mcpClient.sendRequest('tools/call', {
                name: 'list_records',
                arguments: { collection: 'students' }
              });
              
              if (studentsResult.content && studentsResult.content[0]) {
                const studentsText = studentsResult.content[0].text;
                const studentCount = (studentsText.match(/Found (\d+) records/)?.[1] || '0');
                console.log(`📊 Students记录数量: ${studentCount}`);
              }
            }
          }
          
          // 5. 总结和建议
          console.log('\n📋 Step 5: Summary and Recommendations');
          console.log('\n🎯 分析总结：');
          console.log('1. 菜单结构已完整获取');
          console.log('2. 找到目标页面"123"，当前为空页面');
          console.log('3. Students集合数据已准备就绪');
          console.log('4. 由于Schema API插入问题，建议使用前端界面手动操作');
          
          console.log('\n💡 下一步建议：');
          console.log('1. 通过前端界面在"123"页面添加表格区块');
          console.log('2. 配置表格区块显示Students集合数据');
          console.log('3. 使用MCP工具验证和管理区块内容');
          console.log(`4. 页面URL: https://n.astra.xin/apps/mcp_playground/admin/123`);
          
        } else {
          console.log('❌ 无法解析路由JSON数据');
        }
      } catch (error) {
        console.log('❌ 路由数据解析失败:', error.message);
        console.log('📄 原始响应:', routesText.substring(0, 500) + '...');
      }
    }

  } catch (error) {
    console.error('\n❌ Analysis failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    console.log('\n🔌 Shutting down server...');
    server.kill();
  }
}

// 启动分析
analyzeMenuStructure().catch(console.error);
