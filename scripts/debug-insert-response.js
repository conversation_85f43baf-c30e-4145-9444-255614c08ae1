import axios from 'axios';
import testConfig from './test-config.js';

// 创建 axios 客户端
const client = axios.create({
  baseURL: testConfig.baseUrl,
  headers: {
    'Authorization': `Bearer ${testConfig.token}`,
    'X-App': testConfig.app,
    'Content-Type': 'application/json'
  }
});

function generateUID() {
  return Math.random().toString(36).substring(2, 15);
}

async function debugInsertResponse() {
  console.log('🔍 调试 insert 方法的响应结构');
  
  try {
    const insertSchema = {
      type: 'void',
      'x-component': 'Markdown.Void',
      'x-component-props': {
        content: '# 调试测试\n\n检查insert响应结构。'
      },
      'x-uid': generateUID(),
      name: generateUID()
    };

    console.log('\n📋 Insert schema:');
    console.log(JSON.stringify(insertSchema, null, 2));

    const insertResponse = await client.post('/uiSchemas:insert', {
      values: insertSchema
    });

    console.log('\n📥 Complete insert response:');
    console.log(JSON.stringify(insertResponse.data, null, 2));

    console.log('\n🔍 Response structure analysis:');
    console.log('- response.data:', typeof insertResponse.data);
    console.log('- response.data.data:', typeof insertResponse.data.data);
    
    if (insertResponse.data.data) {
      console.log('- response.data.data keys:', Object.keys(insertResponse.data.data));
      console.log('- response.data.data["x-uid"]:', insertResponse.data.data['x-uid']);
      console.log('- response.data.data.values:', insertResponse.data.data.values);
      
      if (insertResponse.data.data.values) {
        console.log('- response.data.data.values["x-uid"]:', insertResponse.data.data.values['x-uid']);
      }
    }

    // 测试获取刚创建的schema
    if (insertResponse.data.data && insertResponse.data.data['x-uid']) {
      console.log('\n📋 Testing getJsonSchema for created block:');
      const createdUid = insertResponse.data.data['x-uid'];
      
      try {
        const getResponse = await client.get(`/uiSchemas:getJsonSchema/${createdUid}`);
        console.log('✅ getJsonSchema successful:');
        console.log(JSON.stringify(getResponse.data, null, 2));
      } catch (error) {
        console.log('❌ getJsonSchema failed:', error.response?.data || error.message);
      }
    }

  } catch (error) {
    console.error('❌ Debug failed:', error.response?.data || error.message);
  }
}

debugInsertResponse().catch(console.error);
