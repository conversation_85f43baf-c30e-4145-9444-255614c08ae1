import { spawn } from 'child_process';
import testConfig from './test-config.js';

let requestId = 1;

// MCP 客户端
class MCPClient {
  constructor(server) {
    this.server = server;
    this.pendingRequests = new Map();
    this.setupResponseHandler();
  }

  setupResponseHandler() {
    let buffer = '';
    this.server.stdout.on('data', (data) => {
      buffer += data.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';
      
      lines.forEach(line => {
        if (line.trim()) {
          try {
            const response = JSON.parse(line);
            this.handleResponse(response);
          } catch (e) {
            // 忽略非 JSON 输出
          }
        }
      });
    });

    this.server.stderr.on('data', (data) => {
      console.log('🔍 Server log:', data.toString());
    });
  }

  handleResponse(response) {
    if (response.id && this.pendingRequests.has(response.id)) {
      const { resolve, reject } = this.pendingRequests.get(response.id);
      this.pendingRequests.delete(response.id);
      
      if (response.error) {
        reject(new Error(response.error.message || 'Unknown error'));
      } else {
        resolve(response.result);
      }
    }
  }

  async sendRequest(method, params = {}, timeout = 15000) {
    const id = requestId++;
    const request = {
      jsonrpc: '2.0',
      id,
      method,
      ...(Object.keys(params).length > 0 && { params })
    };

    console.log(`\n📤 REQUEST: ${method}`);
    if (Object.keys(params).length > 0) {
      console.log('📋 Params:', JSON.stringify(params, null, 2));
    }
    
    return new Promise((resolve, reject) => {
      this.pendingRequests.set(id, { resolve, reject, timestamp: Date.now() });
      
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`Request ${id} timed out after ${timeout}ms`));
        }
      }, timeout);
      
      this.server.stdin.write(JSON.stringify(request) + '\n');
    });
  }
}

function startMCPServer() {
  console.log('🚀 Starting MCP server...');
  const server = spawn('node', [
    'dist/index.js',
    '--base-url', testConfig.baseUrl,
    '--token', testConfig.token,
    '--app', testConfig.app
  ], {
    stdio: ['pipe', 'pipe', 'pipe']
  });

  return server;
}

async function testAlternativeMethod() {
  const server = startMCPServer();
  const mcpClient = new MCPClient(server);
  
  try {
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n=== 🧪 测试新的替代方案 ===\n');

    // 1. 初始化
    console.log('📋 Step 1: Initialize');
    await mcpClient.sendRequest('initialize', {
      protocolVersion: '2024-11-05',
      capabilities: {},
      clientInfo: { name: 'test-client', version: '1.0.0' }
    });

    // 2. 获取页面初始状态
    const pageUid = '2mk30f1pasa';
    console.log('\n📋 Step 2: Get initial page state');
    const initialBlocksResult = await mcpClient.sendRequest('tools/call', {
      name: 'list_page_blocks',
      arguments: { schemaUid: pageUid }
    });
    console.log('✅ Initial blocks response received');
    if (initialBlocksResult.content && initialBlocksResult.content[0]) {
      const initialCount = (initialBlocksResult.content[0].text.match(/Found (\d+) blocks/)?.[1] || '0');
      console.log('📊 Initial block count:', initialCount);
    }

    // 3. 尝试添加Markdown区块（使用新的替代方案）
    console.log('\n📋 Step 3: Add Markdown block using alternative method');
    
    try {
      const addResult = await mcpClient.sendRequest('tools/call', {
        name: 'add_markdown_block',
        arguments: {
          parentUid: pageUid,
          title: '🎉 替代方案测试区块',
          content: '# 成功！\n\n这个区块是通过替代方案创建的：\n\n1. 使用 insert 方法创建区块\n2. 使用 patch 方法添加到 Grid\n\n**创建时间**: ' + new Date().toLocaleString(),
          position: 'beforeEnd'
        }
      });
      
      console.log('\n📥 ADD BLOCK RESPONSE:');
      console.log(JSON.stringify(addResult, null, 2));
      
      if (addResult.isError) {
        console.log('❌ Block creation failed');
      } else {
        console.log('✅ Block creation successful!');
      }
      
    } catch (error) {
      console.log('\n❌ ADD BLOCK ERROR:');
      console.log('Error message:', error.message);
    }

    // 4. 检查区块是否被成功添加
    console.log('\n📋 Step 4: Check if block was added');
    
    try {
      const finalBlocksResult = await mcpClient.sendRequest('tools/call', {
        name: 'list_page_blocks',
        arguments: { schemaUid: pageUid }
      });
      
      console.log('\n📥 FINAL BLOCKS RESPONSE:');
      console.log(JSON.stringify(finalBlocksResult, null, 2));
      
      if (finalBlocksResult.content && finalBlocksResult.content[0]) {
        const finalCount = (finalBlocksResult.content[0].text.match(/Found (\d+) blocks/)?.[1] || '0');
        console.log('📊 Final block count:', finalCount);
        
        if (parseInt(finalCount) > 0) {
          console.log('🎉 SUCCESS! Block was successfully added to the page!');
        } else {
          console.log('😞 Block was not added to the page');
        }
      }
      
    } catch (error) {
      console.log('\n❌ LIST BLOCKS ERROR:');
      console.log('Error message:', error.message);
    }

    // 5. 尝试添加Table区块测试Students数据
    console.log('\n📋 Step 5: Add Students Table block');
    
    try {
      const tableResult = await mcpClient.sendRequest('tools/call', {
        name: 'add_table_block',
        arguments: {
          parentUid: pageUid,
          collectionName: 'students',
          title: '🎓 学生信息表',
          dataSource: 'main',
          position: 'beforeEnd'
        }
      });
      
      console.log('\n📥 ADD TABLE RESPONSE:');
      console.log(JSON.stringify(tableResult, null, 2));
      
      if (tableResult.isError) {
        console.log('❌ Table creation failed');
      } else {
        console.log('✅ Table creation successful!');
      }
      
    } catch (error) {
      console.log('\n❌ ADD TABLE ERROR:');
      console.log('Error message:', error.message);
    }

    // 6. 最终检查
    console.log('\n📋 Step 6: Final check');
    
    try {
      const veryFinalResult = await mcpClient.sendRequest('tools/call', {
        name: 'list_page_blocks',
        arguments: { schemaUid: pageUid }
      });
      
      if (veryFinalResult.content && veryFinalResult.content[0]) {
        const veryFinalCount = (veryFinalResult.content[0].text.match(/Found (\d+) blocks/)?.[1] || '0');
        console.log('📊 Very final block count:', veryFinalCount);
        
        if (parseInt(veryFinalCount) > 0) {
          console.log('🎊 AMAZING! We have successfully added blocks to the page!');
          console.log('🔗 Please check the page at: https://n.astra.xin/apps/mcp_playground/admin/123');
        }
      }
      
    } catch (error) {
      console.log('❌ Final check failed:', error.message);
    }

    console.log('\n🎯 测试完成！');

  } catch (error) {
    console.error('\n❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    console.log('\n🔌 Shutting down server...');
    server.kill();
  }
}

// 启动测试
testAlternativeMethod().catch(console.error);
