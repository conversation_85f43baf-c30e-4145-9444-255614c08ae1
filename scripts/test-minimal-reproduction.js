import axios from 'axios';
import testConfig from './test-config.js';

// 创建 axios 客户端
const client = axios.create({
  baseURL: testConfig.baseUrl,
  headers: {
    'Authorization': `Bearer ${testConfig.token}`,
    'X-App': testConfig.app,
    'Content-Type': 'application/json'
  }
});

// 简单的UID生成函数
function generateUID() {
  return Math.random().toString(36).substring(2, 15);
}

async function testMinimalReproduction() {
  console.log('🔍 最小可复现案例测试');
  console.log('目标：对比 insert 和 insertAdjacent 方法的差异\n');
  
  const pageUid = '2mk30f1pasa'; // "123" 页面
  const gridUid = 'v4pv8gtz2s3'; // Grid UID
  
  try {
    // 1. 测试直接 insert 方法（已知可以工作）
    console.log('📋 Test 1: Direct insert method (known to work)');
    
    const insertSchema = {
      type: 'void',
      'x-component': 'Markdown.Void',
      'x-component-props': {
        content: '# 直接插入测试\n\n这个区块通过insert方法创建。'
      },
      'x-uid': generateUID(),
      name: generateUID()
    };

    try {
      const insertResponse = await client.post('/uiSchemas:insert', {
        values: insertSchema
      });
      console.log('✅ Direct insert successful');
      console.log('📊 Created UID:', insertResponse.data.data['x-uid']);
    } catch (error) {
      console.log('❌ Direct insert failed:', error.response?.data || error.message);
    }

    // 2. 测试最简单的 insertAdjacent（已知失败）
    console.log('\n📋 Test 2: Simple insertAdjacent (known to fail)');
    
    const adjacentSchema = {
      type: 'void',
      'x-component': 'Markdown.Void',
      'x-component-props': {
        content: '# Adjacent插入测试\n\n这个区块通过insertAdjacent方法创建。'
      },
      'x-uid': generateUID(),
      name: generateUID()
    };

    try {
      const adjacentResponse = await client.post('/uiSchemas:insertAdjacent', {
        parentUid: gridUid,
        schema: adjacentSchema,
        position: 'beforeEnd'
      });
      console.log('✅ insertAdjacent successful (unexpected!)');
      console.log('📊 Created UID:', adjacentResponse.data.data['x-uid']);
    } catch (error) {
      console.log('❌ insertAdjacent failed (expected)');
      console.log('📊 Error:', error.response?.data?.errors?.[0]?.message || error.message);
    }

    // 3. 测试不同的 insertAdjacent 参数组合
    console.log('\n📋 Test 3: Different insertAdjacent parameter combinations');
    
    const testCases = [
      {
        name: 'Without name property',
        schema: {
          type: 'void',
          'x-component': 'Markdown.Void',
          'x-component-props': { content: '# Test without name' },
          'x-uid': generateUID()
        }
      },
      {
        name: 'Without x-uid property',
        schema: {
          type: 'void',
          'x-component': 'Markdown.Void',
          'x-component-props': { content: '# Test without x-uid' },
          name: generateUID()
        }
      },
      {
        name: 'Minimal schema',
        schema: {
          type: 'void',
          'x-component': 'Markdown.Void'
        }
      },
      {
        name: 'With wrap parameter',
        schema: {
          type: 'void',
          'x-component': 'Markdown.Void',
          'x-component-props': { content: '# Test with wrap' },
          'x-uid': generateUID(),
          name: generateUID()
        },
        wrap: {
          type: 'void',
          'x-component': 'CardItem',
          'x-component-props': { title: 'Wrapped Block' }
        }
      }
    ];

    for (const testCase of testCases) {
      console.log(`\n🧪 Testing: ${testCase.name}`);
      
      const requestBody = {
        parentUid: gridUid,
        schema: testCase.schema,
        position: 'beforeEnd'
      };
      
      if (testCase.wrap) {
        requestBody.wrap = testCase.wrap;
      }
      
      try {
        const response = await client.post('/uiSchemas:insertAdjacent', requestBody);
        console.log('✅ Success (unexpected!)');
        console.log('📊 Created UID:', response.data.data['x-uid']);
      } catch (error) {
        console.log('❌ Failed (expected)');
        console.log('📊 Error:', error.response?.data?.errors?.[0]?.message || error.message);
      }
    }

    // 4. 测试使用页面UID而不是Grid UID
    console.log('\n📋 Test 4: Using page UID instead of grid UID');
    
    const pageSchema = {
      type: 'void',
      'x-component': 'Markdown.Void',
      'x-component-props': {
        content: '# 页面级插入测试\n\n使用页面UID而不是Grid UID。'
      },
      'x-uid': generateUID(),
      name: generateUID()
    };

    try {
      const pageResponse = await client.post('/uiSchemas:insertAdjacent', {
        parentUid: pageUid, // 使用页面UID
        schema: pageSchema,
        position: 'beforeEnd'
      });
      console.log('✅ Page-level insert successful (unexpected!)');
      console.log('📊 Created UID:', pageResponse.data.data['x-uid']);
    } catch (error) {
      console.log('❌ Page-level insert failed (expected)');
      console.log('📊 Error:', error.response?.data?.errors?.[0]?.message || error.message);
    }

    // 5. 检查最终页面状态
    console.log('\n📋 Test 5: Check final page state');
    
    try {
      const finalPageResponse = await client.get(`/uiSchemas:getJsonSchema/${pageUid}?includeAsyncNode=true`);
      const pageSchema = finalPageResponse.data.data;
      
      console.log('✅ Final page schema retrieved');
      console.log('📊 Page has properties:', !!pageSchema.properties);
      
      if (pageSchema.properties && pageSchema.properties.ljascd3pvvg) {
        const grid = pageSchema.properties.ljascd3pvvg;
        console.log('📊 Grid has properties:', !!grid.properties);
        if (grid.properties) {
          console.log('📊 Grid properties count:', Object.keys(grid.properties).length);
          console.log('📊 Grid property keys:', Object.keys(grid.properties));
        }
      }
    } catch (error) {
      console.log('❌ Failed to get final page state:', error.message);
    }

    console.log('\n🎯 测试结论：');
    console.log('1. insert 方法可以正常工作');
    console.log('2. insertAdjacent 方法始终失败，错误信息一致');
    console.log('3. 问题不在于Schema结构，而在于insertAdjacent的内部实现');
    console.log('4. 可能的解决方案：使用insert方法替代insertAdjacent');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testMinimalReproduction().catch(console.error);
