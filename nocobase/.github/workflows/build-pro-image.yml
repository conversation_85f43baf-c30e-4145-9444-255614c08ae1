name: Build pro image

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  workflow_dispatch:
  # push:
  #   branches:
  #     - 'main'
  #     - 'next'
  #     - 'develop'
  #   paths:
  #     - 'packages/**'
  #     - 'Dockerfile'
  #     - '.github/workflows/build-pro-image.yml'
  # pull_request:
  #   branches:
  #     - '**'
  #   paths:
  #     - 'packages/**'
  #     - 'docker/nocobase/**'
  #     - 'Dockerfile'
  #     - '.github/workflows/build-docker-image.yml'

jobs:
  get-plugins:
    uses: nocobase/nocobase/.github/workflows/get-plugins.yml@main
    secrets: inherit
  build-and-push:
    if: github.event.pull_request.head.repo.fork != true
    runs-on: ubuntu-latest
    needs: get-plugins
    services:
      verdaccio:
        image: verdaccio/verdaccio:5
        ports:
          - 4873:4873
    steps:
      - name: Get pro plugins
        id: get-pro-plugins
        run: |
          if [[ "${{ github.head_ref || github.ref_name }}" == "next" ]]; then
            echo "proRepos=$(echo '${{ needs.get-plugins.outputs.beta-plugins }}')" >> $GITHUB_OUTPUT
          elif [[ "${{ github.head_ref || github.ref_name }}" == "develop" ]]; then
            echo "proRepos=$(echo '${{ needs.get-plugins.outputs.alpha-plugins }}')" >> $GITHUB_OUTPUT
          else
            echo "proRepos=$(echo '${{ needs.get-plugins.outputs.rc-plugins }}')" >> $GITHUB_OUTPUT
          fi
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: nocobase,pro-plugins,${{ join(fromJSON(steps.get-pro-plugins.outputs.proRepos), ',') }}
          skip-token-revoke: true
      - name: Checkout
        uses: actions/checkout@v3
        with:
          token: ${{ steps.app-token.outputs.token }}
          submodules: true
      - name: Checkout pro-plugins
        uses: actions/checkout@v3
        with:
          repository: nocobase/pro-plugins
          ref: main
          path: packages/pro-plugins
          fetch-depth: 0
          token: ${{ steps.app-token.outputs.token }}
      - run: |
          cd packages/pro-plugins &&
          if git show-ref --quiet refs/remotes/origin/${{ github.head_ref || github.ref_name }}; then
            git checkout ${{ github.head_ref || github.ref_name }}
          else
            if git show-ref --quiet refs/remotes/origin/${{ github.event.pull_request.base.ref }}; then
              git checkout ${{ github.event.pull_request.base.ref }}
            else
              git checkout next
            fi
          fi
      - name: Clone pro repos
        shell: bash
        run: |
          for repo in ${{ join(fromJSON(steps.get-pro-plugins.outputs.proRepos), ' ') }}
          do
          git clone -b main https://x-access-token:${{ steps.app-token.outputs.token }}@github.com/nocobase/$repo.git packages/pro-plugins/@nocobase/$repo
          done
      - run: |
          for repo in ${{ join(fromJSON(steps.get-pro-plugins.outputs.proRepos), ' ') }}
          do
            cd ./packages/pro-plugins/@nocobase/$repo
            if git show-ref --quiet refs/remotes/origin/${{ github.head_ref || github.ref_name }}; then
              git checkout ${{ github.head_ref || github.ref_name }}
            else
              if git show-ref --quiet refs/remotes/origin/${{ github.event.pull_request.base.ref }}; then
                git checkout ${{ github.event.pull_request.base.ref }}
              else
                git checkout next
              fi
            fi
            cd ../../../../
          done
      - name: rm .git
        run: |
          rm -rf packages/pro-plugins/.git
          for repo in ${{ join(fromJSON(steps.get-pro-plugins.outputs.proRepos), ' ') }}
          do
            rm -rf packages/pro-plugins/@nocobase/$repo/.git
          done
          git config --global user.email "<EMAIL>"
          git config --global user.name "Your Name" && git add -A && git commit -m "tmp commit"
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          driver-opts: network=host
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            nocobase/nocobase
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}

      - name: Login to Aliyun Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ secrets.ALI_DOCKER_REGISTRY }}
          username: ${{ secrets.ALI_DOCKER_USERNAME }}
          password: ${{ secrets.ALI_DOCKER_PASSWORD }}

      - name: Set tags
        id: set-tags
        run: |
          echo "::set-output name=tags::${{ secrets.ALI_DOCKER_REGISTRY }}/${{ steps.meta.outputs.tags }}"
      - name: Set variables
        run: |
          target_directory="./packages/pro-plugins/@nocobase"
          subdirectories=$(find "$target_directory" -mindepth 1 -maxdepth 1 -type d -exec basename {} \; | tr '\n' ' ')
          trimmed_variable=$(echo "$subdirectories" | xargs)
          packageNames="@nocobase/${trimmed_variable// / @nocobase/}"
          pluginNames="${trimmed_variable//plugin-/}"
          BEFORE_PACK_NOCOBASE="yarn add @nocobase/plugin-notifications @nocobase/plugin-disable-pm-add $packageNames -W --production"
          APPEND_PRESET_LOCAL_PLUGINS="notifications,disable-pm-add,${pluginNames// /,}"
          echo "var1=$BEFORE_PACK_NOCOBASE" >> $GITHUB_OUTPUT
          echo "var2=$APPEND_PRESET_LOCAL_PLUGINS" >> $GITHUB_OUTPUT
        id: vars
      - name: Build and push
        uses: docker/build-push-action@v3
        with:
          context: .
          file: Dockerfile.pro
          build-args: |
            VERDACCIO_URL=http://localhost:4873/
            COMMIT_HASH=${GITHUB_SHA}
            PLUGINS_DIRS=pro-plugins
            BEFORE_PACK_NOCOBASE=${{ steps.vars.outputs.var1 }}
            APPEND_PRESET_LOCAL_PLUGINS=${{ steps.vars.outputs.var2 }}
          push: true
          tags: ${{ steps.set-tags.outputs.tags }}
      - name: Deploy NocoBase
        env:
          IMAGE_TAG: ${{ steps.meta.outputs.tags }}
        run: |
          echo $IMAGE_TAG
          export APP_NAME=$(echo $IMAGE_TAG | cut -d ":" -f 2)
          echo $APP_NAME
          curl --retry 2 --location --request POST "${{secrets.NOCOBASE_DEPLOY_HOST}}$APP_NAME" \
            --header 'Content-Type: application/json' \
            -d "{
                \"tag\": \"$APP_NAME\",
                \"dialect\": \"postgres\"
            }"
      - name: Deploy NocoBase V2
        env:
          IMAGE_TAG: ${{ steps.meta.outputs.tags }}
        run: |
          echo $IMAGE_TAG
          export APP_NAME=$(echo $IMAGE_TAG | cut -d ":" -f 2)
          echo $APP_NAME
          curl --retry 2 --location --request POST "${{secrets.NOCOBASE_DEPLOY_HOST_V2}}$APP_NAME" \
            --header 'Content-Type: application/json' \
            -d "{
                \"tag\": \"$APP_NAME\",
                \"dialect\": \"postgres\"
            }"
