name: Get plugins

on:
  workflow_call:
    secrets:
      NOCOBASE_APP_PRIVATE_KEY:
        required: true
    outputs:
      all-plugins:
        value: ${{ jobs.get-plugins.outputs.all-plugins }}
      custom-plugins:
        value: ${{ jobs.get-plugins.outputs.custom-plugins }}
      rc-plugins:
        value: ${{ jobs.get-plugins.outputs.rc-plugins }}
      beta-plugins:
        value: ${{ jobs.get-plugins.outputs.beta-plugins }}
      alpha-plugins:
        value: ${{ jobs.get-plugins.outputs.alpha-plugins }}
      unreleased-plugins:
        value: ${{ jobs.get-plugins.outputs.unreleased-plugins }}

jobs:
  get-plugins:
    runs-on: ubuntu-latest
    outputs:
      all-plugins: ${{ steps.get-plugins.outputs.all-plugins }}
      custom-plugins: ${{ steps.get-plugins.outputs.custom-plugins }}
      rc-plugins: ${{ steps.get-plugins.outputs.rc-plugins }}
      beta-plugins: ${{ steps.get-plugins.outputs.beta-plugins }}
      alpha-plugins: ${{ steps.get-plugins.outputs.alpha-plugins }}
      unreleased-plugins: ${{ steps.get-plugins.outputs.unreleased-plugins }}
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          owner: nocobase
          skip-token-revoke: true
      - id: get-plugins
        name: Get plugins
        shell: bash
        run: |
          function retry() {
            local i=0
            local plugins="[]"
            until [ "$i" -ge 2 ]
            do
              plugins=$(gh search repos "props.plugin-type:$1" --owner=nocobase --json name | jq -r 'map(.name) | tostring') && [[ "$plugins" != "[]" ]] && break
              i=$((i+1))
              sleep 10
            done
            echo $plugins
          }
          allPlugins=$(retry custom,rc,beta,alpha,unreleased)
          if [[ "$allPlugins" == "[]" ]]; then
            echo "Get all plugins empty"
            exit 1
          fi
          customPlugins=$(retry custom)
          if [[ "$customPlugins" == "[]" ]]; then
            echo "Get custom plugins empty"
            exit 1
          fi
          rcPlugins=$(retry rc)
          if [[ "$rcPlugins" == "[]" ]]; then
            echo "Get rc plugins empty"
            exit 1
          fi
          betaPlugins=$(retry beta,rc)
          if [[ "$betaPlugins" == "[]" ]]; then
            echo "Get beta plugins empty"
            exit 1
          fi
          alphaPlugins=$(retry alpha,beta,rc)
          if [[ "$alphaPlugins" == "[]" ]]; then
            echo "Get alpha plugins empty"
            exit 1
          fi
          unreleasedPlugins=$(retry unreleased)
          echo "all-plugins=$allPlugins" >> "$GITHUB_OUTPUT"
          echo "custom-plugins=$customPlugins" >> "$GITHUB_OUTPUT"
          echo "rc-plugins=$rcPlugins" >> "$GITHUB_OUTPUT"
          echo "beta-plugins=$betaPlugins" >> "$GITHUB_OUTPUT"
          echo "alpha-plugins=$alphaPlugins" >> "$GITHUB_OUTPUT"
          echo "unreleased-plugins=$unreleasedPlugins" >> "$GITHUB_OUTPUT"
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
