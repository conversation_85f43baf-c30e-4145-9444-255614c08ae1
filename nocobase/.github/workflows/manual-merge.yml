name: Manual merge

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

on:
  workflow_dispatch:

jobs:
  get-plugins:
    uses: nocobase/nocobase/.github/workflows/get-plugins.yml@main
    secrets: inherit
  get-repos:
    runs-on: ubuntu-latest
    needs: get-plugins
    outputs:
      proRepos: ${{ steps.get-repos.outputs.proRepos }}
    steps:
      - name: Get repos
        id: get-repos
        run: |
          if [[ "${{ github.head_ref || github.ref_name }}" == "next" ]]; then
            echo "proRepos=$(echo '${{ needs.get-plugins.outputs.beta-plugins }}')" >> $GITHUB_OUTPUT
          elif [[ "${{ github.head_ref || github.ref_name }}" == "develop" ]]; then
            echo "proRepos=$(echo '${{ needs.get-plugins.outputs.alpha-plugins }}')" >> $GITHUB_OUTPUT
          else
            exit 1
          fi
  merge:
    runs-on: ubuntu-latest
    needs:
      - get-plugins
      - get-repos
    strategy:
      fail-fast: false
      matrix:
        repo:
          - 'nocobase'
          - 'pro-plugins'
          - ${{ fromJSON(needs.get-repos.outputs.proRepos) }}
          - ${{ fromJSON(needs.get-plugins.outputs.custom-plugins) }}
    steps:
      - name: Get info
        id: get-info
        run: |
          if [[ "${{ github.head_ref || github.ref_name }}" == "next" ]]; then
            echo "baseBranch=$(echo 'next')" >> $GITHUB_OUTPUT
            echo "targetBranch=$(echo 'main')" >> $GITHUB_OUTPUT
          elif [[ "${{ github.head_ref || github.ref_name }}" == "develop" ]]; then
            echo "baseBranch=$(echo 'develop')" >> $GITHUB_OUTPUT
            echo "targetBranch=$(echo 'next')" >> $GITHUB_OUTPUT
          else
            exit 1
          fi
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: ${{ matrix.repo }}
          skip-token-revoke: true
      - name: Get GitHub App User ID
        id: get-user-id
        run: echo "user-id=$(gh api "/users/${{ steps.app-token.outputs.app-slug }}[bot]" --jq .id)" >> "$GITHUB_OUTPUT"
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
      - name: Set user
        run: |
          git config --global user.name '${{ steps.app-token.outputs.app-slug }}[bot]'
          git config --global user.email '${{ steps.get-user-id.outputs.user-id }}+${{ steps.app-token.outputs.app-slug }}[bot]@users.noreply.github.com>'
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ steps.get-info.outputs.baseBranch }}
          repository: nocobase/${{ matrix.repo }}
          fetch-depth: 0
          token: ${{ steps.app-token.outputs.token }}
      - name: Merge ${{ steps.get-info.outputs.targetBranch }} into ${{ steps.get-info.outputs.baseBranch }}
        shell: bash
        run: |
          git checkout ${{ steps.get-info.outputs.targetBranch }}
          git pull origin ${{ steps.get-info.outputs.targetBranch }}
          git checkout ${{ steps.get-info.outputs.baseBranch }}
          git pull origin ${{ steps.get-info.outputs.baseBranch }}
          git merge ${{ steps.get-info.outputs.targetBranch }}
          git push origin ${{ steps.get-info.outputs.baseBranch }} --atomic --tags
      - name: Merge ${{ steps.get-info.outputs.baseBranch }} into ${{ steps.get-info.outputs.targetBranch }}
        shell: bash
        run: |
          git checkout ${{ steps.get-info.outputs.targetBranch }}
          git merge ${{ steps.get-info.outputs.baseBranch }}
          git push origin ${{ steps.get-info.outputs.targetBranch }} --atomic --tags
