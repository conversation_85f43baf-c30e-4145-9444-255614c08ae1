name: Manual release next

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  workflow_dispatch:
    inputs:
      is_feat:
        description: 'is feat'
        type: boolean
      add_minor:
        description: 'add minor number'
        type: boolean
      version:
        description: 'specific a version'
        type: 'string'

jobs:
  get-plugins:
    uses: nocobase/nocobase/.github/workflows/get-plugins.yml@main
    secrets: inherit
  pre-merge-next-into-develop:
    needs: get-plugins
    runs-on: ubuntu-latest
    strategy:
      matrix:
        repo:
          - 'nocobase'
          - 'pro-plugins'
          - ${{ fromJSON(needs.get-plugins.outputs.beta-plugins) }}
          - ${{ fromJSON(needs.get-plugins.outputs.custom-plugins) }}
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: nocobase,pro-plugins,${{ join(fromJSON(needs.get-plugins.outputs.beta-plugins), ',') }},${{ join(fromJSON(needs.get-plugins.outputs.custom-plugins), ',') }}
          skip-token-revoke: true
      - name: Get GitHub App User ID
        id: get-user-id
        run: echo "user-id=$(gh api "/users/${{ steps.app-token.outputs.app-slug }}[bot]" --jq .id)" >> "$GITHUB_OUTPUT"
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
      - name: Checkout
        uses: actions/checkout@v4
        with:
          # ref: 'main'
          repository: nocobase/${{ matrix.repo }}
          fetch-depth: 0
          token: ${{ steps.app-token.outputs.token }}
      - name: next -> develop (nocobase/${{ matrix.repo }})
        run: |
          git config --global user.name '${{ steps.app-token.outputs.app-slug }}[bot]'
          git config --global user.email '${{ steps.get-user-id.outputs.user-id }}+${{ steps.app-token.outputs.app-slug }}[bot]@users.noreply.github.com>'
          git checkout next
          git pull origin next
          git checkout develop
          git merge next
          git push origin develop --tags --atomic
  update-version:
    needs:
      - get-plugins
      - pre-merge-next-into-develop
    runs-on: ubuntu-latest
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: nocobase,pro-plugins,${{ join(fromJSON(needs.get-plugins.outputs.beta-plugins), ',') }},${{ join(fromJSON(needs.get-plugins.outputs.custom-plugins), ',') }}
          skip-token-revoke: true
      - name: Get GitHub App User ID
        id: get-user-id
        run: echo "user-id=$(gh api "/users/${{ steps.app-token.outputs.app-slug }}[bot]" --jq .id)" >> "$GITHUB_OUTPUT"
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: nocobase/nocobase
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: true
          fetch-depth: 0
      - name: Checkout pro-plugins
        uses: actions/checkout@v4
        with:
          repository: nocobase/pro-plugins
          path: packages/pro-plugins
          fetch-depth: 0
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: true
      - name: Clone pro repos
        shell: bash
        run: |
          for repo in ${{ join(fromJSON(needs.get-plugins.outputs.beta-plugins), ' ') }} ${{ join(fromJSON(needs.get-plugins.outputs.custom-plugins), ' ') }}
          do
          git clone -b next https://x-access-token:${{ steps.app-token.outputs.token }}@github.com/nocobase/$repo.git packages/pro-plugins/@nocobase/$repo
          done
      - name: Set Node.js 20
        uses: actions/setup-node@v3
        with:
          node-version: 20
      - name: Checkout
        shell: bash
        run: |
          cd ./packages/pro-plugins
          git checkout next
          for repo in ${{ join(fromJSON(needs.get-plugins.outputs.beta-plugins), ' ') }} ${{ join(fromJSON(needs.get-plugins.outputs.custom-plugins), ' ') }}
          do
            echo "@nocobase/$repo" >> .git/info/exclude
          done
          echo "$(<.git/info/exclude )"
          cd ./../..
          git checkout next
          git config --global user.name '${{ steps.app-token.outputs.app-slug }}[bot]'
          git config --global user.email '${{ steps.get-user-id.outputs.user-id }}+${{ steps.app-token.outputs.app-slug }}[bot]@users.noreply.github.com>'
          echo "packages/pro-plugins" >> .git/info/exclude
      - name: yarn install and build
        run: |
          yarn config set registry https://registry.npmjs.org/
          yarn install
          yarn build
      - name: Install Lerna
        run: npm install -g lerna@4
      - name: Run release.sh
        shell: bash
        run: |
          bash release.sh $VERSION $IS_FEAT $ADD_MINOR
        env:
          PRO_PLUGIN_REPOS: ${{ needs.get-plugins.outputs.beta-plugins }}
          CUSTOM_PRO_PLUGIN_REPOS: ${{ needs.get-plugins.outputs.custom-plugins }}
          VERSION: ${{ inputs.version && format('--version {0}', inputs.version) || '' }}
          IS_FEAT: ${{ inputs.is_feat && '--is-feat' || '' }}
          ADD_MINOR: ${{ inputs.add_minor && '--add-minor' || '' }}
      - name: Push and merge into next
        run: |
          for repo in ${{ join(fromJSON(needs.get-plugins.outputs.beta-plugins), ' ') }} ${{ join(fromJSON(needs.get-plugins.outputs.custom-plugins), ' ') }}
          do
            cd ./packages/pro-plugins/@nocobase/$repo
            git push origin next --atomic --tags
            git checkout develop
            git merge -X ours next --no-edit
            git push origin develop --tags --atomic
            cd ../../../../
          done
          cd ./packages/pro-plugins
          git push origin next --atomic --tags
          git checkout develop
          git merge -X ours next --no-edit
          git push origin develop --tags --atomic
          cd ../../
          git push origin next --atomic --tags
          git checkout develop
          git merge -X ours next --no-edit
          git push origin develop --tags --atomic
