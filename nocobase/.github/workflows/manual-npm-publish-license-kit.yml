name: Manual npm publish license-kit
env:
  DEBUG: napi:*
  APP_NAME: license-kit
  MACOSX_DEPLOYMENT_TARGET: '10.13'
permissions:
  contents: write
  id-token: write
on:
  workflow_dispatch:

# 'on':
#   push:
#     branches:
#       - main
#     tags-ignore:
#       - '**'
#     paths-ignore:
#       - '**/*.md'
#       - LICENSE
#       - '**/*.gitignore'
#       - .editorconfig
#       - docs/**
#   pull_request: null
jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        settings:
          - host: macos-latest
            target: x86_64-apple-darwin
            build: yarn build --target x86_64-apple-darwin
          - host: windows-latest
            build: yarn build --target x86_64-pc-windows-msvc
            target: x86_64-pc-windows-msvc
          - host: windows-latest
            build: yarn build --target i686-pc-windows-msvc
            target: i686-pc-windows-msvc
          - host: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            docker: ghcr.io/napi-rs/napi-rs/nodejs-rust:lts-debian
            build: yarn build --target x86_64-unknown-linux-gnu
          - host: ubuntu-latest
            target: x86_64-unknown-linux-musl
            docker: ghcr.io/napi-rs/napi-rs/nodejs-rust:lts-alpine
            build: yarn build --target x86_64-unknown-linux-musl
          - host: macos-latest
            target: aarch64-apple-darwin
            build: yarn build --target aarch64-apple-darwin
          - host: ubuntu-latest
            target: aarch64-unknown-linux-gnu
            docker: ghcr.io/napi-rs/napi-rs/nodejs-rust:lts-debian-aarch64
            build: yarn build --target aarch64-unknown-linux-gnu
          - host: ubuntu-latest
            target: armv7-unknown-linux-gnueabihf
            setup: |
              sudo apt-get update
              sudo apt-get install gcc-arm-linux-gnueabihf -y
            build: yarn build --target armv7-unknown-linux-gnueabihf
          - host: ubuntu-latest
            target: armv7-unknown-linux-musleabihf
            build: yarn build --target armv7-unknown-linux-musleabihf
          - host: ubuntu-latest
            target: aarch64-linux-android
            build: yarn build --target aarch64-linux-android
          - host: ubuntu-latest
            target: armv7-linux-androideabi
            build: yarn build --target armv7-linux-androideabi
          - host: ubuntu-latest
            target: aarch64-unknown-linux-musl
            docker: ghcr.io/napi-rs/napi-rs/nodejs-rust:lts-alpine
            build: |-
              set -e &&
              rustup target add aarch64-unknown-linux-musl &&
              yarn build --target aarch64-unknown-linux-musl
          - host: windows-latest
            target: aarch64-pc-windows-msvc
            build: yarn build --target aarch64-pc-windows-msvc
          - host: ubuntu-latest
            target: riscv64gc-unknown-linux-gnu
            setup: |
              sudo apt-get update
              sudo apt-get install gcc-riscv64-linux-gnu -y
            build: yarn build --target riscv64gc-unknown-linux-gnu
    name: stable - ${{ matrix.settings.target }} - node@20
    runs-on: ${{ matrix.settings.host }}
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: license-kit
          skip-token-revoke: true
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: nocobase/license-kit
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: true
          ref: main
      - name: Setup node
        uses: actions/setup-node@v4
        if: ${{ !matrix.settings.docker }}
        with:
          node-version: 20
          cache: yarn
      - name: Install
        uses: dtolnay/rust-toolchain@stable
        if: ${{ !matrix.settings.docker }}
        with:
          toolchain: stable
          targets: ${{ matrix.settings.target }}
      - name: Cache cargo
        uses: actions/cache@v4
        with:
          path: |
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            .cargo-cache
            target/
          key: ${{ matrix.settings.target }}-cargo-${{ matrix.settings.host }}
      - uses: goto-bus-stop/setup-zig@v2
        if: ${{ matrix.settings.target == 'armv7-unknown-linux-gnueabihf' || matrix.settings.target == 'armv7-unknown-linux-musleabihf' }}
        with:
          version: 0.13.0
      - name: Setup toolchain
        run: ${{ matrix.settings.setup }}
        if: ${{ matrix.settings.setup }}
        shell: bash
      - name: Setup node x86
        if: matrix.settings.target == 'i686-pc-windows-msvc'
        run: yarn config set supportedArchitectures.cpu "ia32"
        shell: bash
      - name: Install dependencies
        run: yarn install
      - name: Setup node x86
        uses: actions/setup-node@v4
        if: matrix.settings.target == 'i686-pc-windows-msvc'
        with:
          node-version: 20
          cache: yarn
          architecture: x86
      - name: Install OpenSSL dev
        if: ${{ matrix.settings.host == 'ubuntu-latest' }}
        run: |
          sudo apt-get update
          sudo apt-get install -y pkg-config libssl-dev
      - name: Build in docker
        uses: addnab/docker-run-action@v3
        if: ${{ matrix.settings.docker }}
        with:
          image: ${{ matrix.settings.docker }}
          options: '--user 0:0 -v ${{ github.workspace }}/.cargo-cache/git/db:/usr/local/cargo/git/db -v ${{ github.workspace }}/.cargo/registry/cache:/usr/local/cargo/registry/cache -v ${{ github.workspace }}/.cargo/registry/index:/usr/local/cargo/registry/index -v ${{ github.workspace }}:/build -w /build'
          run: ${{ matrix.settings.build }}
      - name: Build
        run: ${{ matrix.settings.build }}
        if: ${{ !matrix.settings.docker }}
        shell: bash
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: bindings-${{ matrix.settings.target }}
          path: ${{ env.APP_NAME }}.*.node
          if-no-files-found: error
  build-freebsd:
    runs-on: ubuntu-latest
    name: Build FreeBSD
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: license-kit
          skip-token-revoke: true
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: nocobase/license-kit
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: true
          ref: main
      - name: Build
        id: build
        uses: cross-platform-actions/action@v0.27.0
        env:
          DEBUG: napi:*
          RUSTUP_IO_THREADS: 1
        with:
          operating_system: freebsd
          version: '14.2'
          memory: 8G
          cpu_count: 3
          environment_variables: 'DEBUG RUSTUP_IO_THREADS'
          shell: bash
          run: |
            sudo pkg install -y -f curl node libnghttp2 npm openssl
            sudo npm install -g yarn --ignore-scripts
            curl https://sh.rustup.rs -sSf --output rustup.sh
            sh rustup.sh -y --profile minimal --default-toolchain beta
            source "$HOME/.cargo/env"
            echo "~~~~ rustc --version ~~~~"
            rustc --version
            echo "~~~~ node -v ~~~~"
            node -v
            echo "~~~~ yarn --version ~~~~"
            yarn --version
            pwd
            ls -lah
            whoami
            env
            freebsd-version
            yarn install
            yarn build
            rm -rf node_modules
            rm -rf target
            rm -rf .yarn/cache
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: bindings-freebsd
          path: ${{ env.APP_NAME }}.*.node
          if-no-files-found: error
  test-macOS-windows-binding:
    name: Test bindings on ${{ matrix.settings.target }} - node@${{ matrix.node }}
    needs:
      - build
    strategy:
      fail-fast: false
      matrix:
        settings:
          - host: macos-latest
            target: x86_64-apple-darwin
          - host: windows-latest
            target: x86_64-pc-windows-msvc
        node:
          - '18'
          - '20'
    runs-on: ${{ matrix.settings.host }}
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: license-kit
          skip-token-revoke: true
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: nocobase/license-kit
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: true
          ref: main
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: yarn
          architecture: x64
      - name: Install dependencies
        run: yarn install
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: bindings-${{ matrix.settings.target }}
          path: .
      - name: List packages
        run: ls -R .
        shell: bash
      - name: Test bindings
        run: yarn test
  test-linux-x64-gnu-binding:
    name: Test bindings on Linux-x64-gnu - node@${{ matrix.node }}
    needs:
      - build
    strategy:
      fail-fast: false
      matrix:
        node:
          - '18'
          - '20'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: license-kit
          skip-token-revoke: true
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: nocobase/license-kit
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: true
          ref: main
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: yarn
      - name: Install dependencies
        run: yarn install
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: bindings-x86_64-unknown-linux-gnu
          path: .
      - name: List packages
        run: ls -R .
        shell: bash
      - name: Test bindings
        run: docker run --rm -v $(pwd):/build -w /build node:${{ matrix.node }}-slim yarn test
  test-linux-x64-musl-binding:
    name: Test bindings on x86_64-unknown-linux-musl - node@${{ matrix.node }}
    needs:
      - build
    strategy:
      fail-fast: false
      matrix:
        node:
          - '18'
          - '20'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: license-kit
          skip-token-revoke: true
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: nocobase/license-kit
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: true
          ref: main
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node }}
          cache: yarn
      - name: Install dependencies
        run: |
          yarn config set supportedArchitectures.libc "musl"
          yarn install
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: bindings-x86_64-unknown-linux-musl
          path: .
      - name: List packages
        run: ls -R .
        shell: bash
      - name: Test bindings
        run: docker run --rm -v $(pwd):/build -w /build node:${{ matrix.node }}-alpine yarn test
  test-linux-aarch64-gnu-binding:
    name: Test bindings on aarch64-unknown-linux-gnu - node@${{ matrix.node }}
    needs:
      - build
    strategy:
      fail-fast: false
      matrix:
        node:
          - '18'
          - '20'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: license-kit
          skip-token-revoke: true
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: nocobase/license-kit
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: true
          ref: main
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: bindings-aarch64-unknown-linux-gnu
          path: .
      - name: List packages
        run: ls -R .
        shell: bash
      - name: Install dependencies
        run: |
          yarn config set supportedArchitectures.cpu "arm64"
          yarn config set supportedArchitectures.libc "glibc"
          yarn install
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: arm64
      - run: docker run --rm --privileged multiarch/qemu-user-static --reset -p yes
      - name: Setup and run tests
        uses: addnab/docker-run-action@v3
        with:
          image: node:${{ matrix.node }}-slim
          options: '--platform linux/arm64 -v ${{ github.workspace }}:/build -w /build'
          run: |
            set -e
            yarn test
            ls -la
  test-linux-aarch64-musl-binding:
    name: Test bindings on aarch64-unknown-linux-musl - node@${{ matrix.node }}
    needs:
      - build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: license-kit
          skip-token-revoke: true
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: nocobase/license-kit
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: true
          ref: main
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: bindings-aarch64-unknown-linux-musl
          path: .
      - name: List packages
        run: ls -R .
        shell: bash
      - name: Install dependencies
        run: |
          yarn config set supportedArchitectures.cpu "arm64"
          yarn config set supportedArchitectures.libc "musl"
          yarn install
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: arm64
      - run: docker run --rm --privileged multiarch/qemu-user-static --reset -p yes
      - name: Setup and run tests
        uses: addnab/docker-run-action@v3
        with:
          image: node:lts-alpine
          options: '--platform linux/arm64 -v ${{ github.workspace }}:/build -w /build'
          run: |
            set -e
            yarn test
  test-linux-arm-gnueabihf-binding:
    name: Test bindings on armv7-unknown-linux-gnueabihf - node@${{ matrix.node }}
    needs:
      - build
    strategy:
      fail-fast: false
      matrix:
        node:
          - '18'
          - '20'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: license-kit
          skip-token-revoke: true
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: nocobase/license-kit
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: true
          ref: main
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: bindings-armv7-unknown-linux-gnueabihf
          path: .
      - name: List packages
        run: ls -R .
        shell: bash
      - name: Install dependencies
        run: |
          yarn config set supportedArchitectures.cpu "arm"
          yarn install
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: arm
      - run: docker run --rm --privileged multiarch/qemu-user-static --reset -p yes
      - name: Setup and run tests
        uses: addnab/docker-run-action@v3
        with:
          image: node:${{ matrix.node }}-bullseye-slim
          options: '--platform linux/arm/v7 -v ${{ github.workspace }}:/build -w /build'
          run: |
            set -e
            yarn test
            ls -la
  universal-macOS:
    name: Build universal macOS binary
    needs:
      - build
    runs-on: macos-latest
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: license-kit
          skip-token-revoke: true
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: nocobase/license-kit
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: true
          ref: main
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: yarn
      - name: Install dependencies
        run: yarn install
      - name: Download macOS x64 artifact
        uses: actions/download-artifact@v4
        with:
          name: bindings-x86_64-apple-darwin
          path: artifacts
      - name: Download macOS arm64 artifact
        uses: actions/download-artifact@v4
        with:
          name: bindings-aarch64-apple-darwin
          path: artifacts
      - name: Combine binaries
        run: yarn universal
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: bindings-universal-apple-darwin
          path: ${{ env.APP_NAME }}.*.node
          if-no-files-found: error
  publish:
    name: Publish
    runs-on: ubuntu-latest
    needs:
      - build-freebsd
      - test-macOS-windows-binding
      - test-linux-x64-gnu-binding
      - test-linux-x64-musl-binding
      - test-linux-aarch64-gnu-binding
      - test-linux-aarch64-musl-binding
      - test-linux-arm-gnueabihf-binding
      - universal-macOS
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.NOCOBASE_APP_ID }}
          private-key: ${{ secrets.NOCOBASE_APP_PRIVATE_KEY }}
          repositories: license-kit
          skip-token-revoke: true
      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: nocobase/license-kit
          token: ${{ steps.app-token.outputs.token }}
          persist-credentials: true
          ref: main
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: yarn
      - name: Install dependencies
        run: yarn install
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts
      - name: Move artifacts
        run: yarn artifacts
      - name: List packages
        run: ls -R ./npm
        shell: bash
      - name: Publish
        run: |
          npm config set provenance true
          if git log -1 --pretty=%B | grep "^[0-9]\+\.[0-9]\+\.[0-9]\+$";
          then
            echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" >> ~/.npmrc
            npm publish --access public
          elif git log -1 --pretty=%B | grep "^[0-9]\+\.[0-9]\+\.[0-9]\+";
          then
            echo "//registry.npmjs.org/:_authToken=$NPM_TOKEN" >> ~/.npmrc
            npm publish --tag next --access public
          else
            echo "Not a release, skipping publish"
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
