{"name": "@nocobase/build", "version": "1.8.14", "description": "Library build tool based on rollup.", "main": "lib/index.js", "types": "./lib/index.d.ts", "bin": {"nocobase-build": "./bin/nocobase-build.js"}, "typings": "./index.d.ts", "dependencies": {"@babel/core": "^7.26.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-modules-amd": "7.24.7", "@babel/preset-env": "^7.26.0", "@hapi/topo": "^6.0.0", "@lerna/project": "4.0.0", "@rsbuild/plugin-babel": "^1.0.3", "@rsdoctor/rspack-plugin": "^0.4.8", "@rspack/core": "1.3.2", "@svgr/webpack": "^8.1.0", "@types/gulp": "^4.0.13", "@types/lerna__package": "5.1.0", "@types/lerna__project": "5.1.0", "@types/tar": "^6.1.5", "@vercel/ncc": "0.36.1", "babel-loader": "^9.2.1", "babel-plugin-syntax-dynamic-import": "^6.18.0", "bundle-require": "^5.1.0", "chalk": "2.4.2", "css-loader": "^6.8.1", "esbuild-register": "^3.4.2", "fast-glob": "^3.3.1", "gulp": "4.0.2", "gulp-typescript": "6.0.0-alpha.1", "javascript-obfuscator": "^4.1.1", "less": "^4.2.0", "less-loader": "^12.2.0", "postcss": "^8.4.29", "postcss-loader": "^7.3.3", "postcss-preset-env": "^9.1.2", "react-imported-component": "^6.5.4", "style-loader": "^3.3.3", "tar": "^7.4.3", "tsup": "8.2.4", "typescript": "5.1.3", "update-notifier": "3.0.0", "vite-plugin-css-injected-by-js": "^3.2.1", "vite-plugin-lib-inject-css": "1.2.0", "yargs-parser": "13.1.2"}, "license": "AGPL-3.0", "scripts": {"build": "tsup", "build:watch": "tsup --watch"}, "gitHead": "d0b4efe4be55f8c79a98a331d99d9f8cf99021a1"}