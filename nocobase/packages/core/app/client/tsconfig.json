{"compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "importHelpers": true, "jsx": "react-jsx", "esModuleInterop": true, "sourceMap": true, "baseUrl": "./", "strict": true, "paths": {"@/*": ["src/*"], "@@/*": ["src/.umi/*"]}, "allowSyntheticDefaultImports": true}, "include": ["mock/**/*", "src/**/*", "config/**/*", ".umirc.ts", "typings.d.ts"], "exclude": ["node_modules", "lib", "es", "public", "dist", "typings", "**/__test__", "test", "docs", "tests"]}